import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:timezone/data/latest.dart' as tz;
import 'package:workmanager/workmanager.dart';
import '../utils/logger.dart';
import '../constants/preference_keys.dart';
import 'reminder_scheduling_service.dart';
import 'notification_queue_manager.dart';
import 'firebase_messaging_service.dart';
import 'foreground_service_manager.dart';

/// Service for managing background monitoring of notification conditions
class BackgroundMonitoringService {
  static final BackgroundMonitoringService _instance =
      BackgroundMonitoringService._internal();

  factory BackgroundMonitoringService() => _instance;
  BackgroundMonitoringService._internal();

  static const String _taskName = 'lekky_notification_check';
  static const String _uniqueName = 'lekky_background_monitor';

  /// Initialize background monitoring
  Future<void> initialize() async {
    try {
      await Workmanager().initialize(
        _callbackDispatcher,
        isInDebugMode: false, // Set to true for debugging
      );
      Logger.info('Background monitoring service initialized');
    } catch (e) {
      Logger.error('Failed to initialize background monitoring: $e');
    }
  }

  /// Start background monitoring (every 5 minutes)
  Future<void> startMonitoring() async {
    try {
      // Cancel any existing tasks first
      await stopMonitoring();

      // Register periodic task (every 6 hours for better battery optimization)
      await Workmanager().registerPeriodicTask(
        _uniqueName,
        _taskName,
        frequency: const Duration(hours: 6),
        constraints: Constraints(
          networkType: NetworkType.not_required,
          requiresBatteryNotLow: false,
          requiresCharging: false,
          requiresDeviceIdle: false, // Allow running even when device is active
          requiresStorageNotLow: false,
        ),
      );

      Logger.info('Background monitoring started (6-hour intervals)');
    } catch (e) {
      Logger.error('Failed to start background monitoring: $e');
    }
  }

  /// Stop background monitoring
  Future<void> stopMonitoring() async {
    try {
      await Workmanager().cancelByUniqueName(_uniqueName);
      Logger.info('Background monitoring stopped');
    } catch (e) {
      Logger.error('Failed to stop background monitoring: $e');
    }
  }

  /// Check if monitoring should be active based on notification settings
  Future<bool> shouldMonitoringBeActive() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final lowBalanceEnabled =
          prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false;
      final timeToTopUpEnabled =
          prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false;
      final invalidRecordEnabled =
          prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false;
      final remindersEnabled =
          prefs.getBool(PreferenceKeys.remindersEnabled) ?? false;

      return (lowBalanceEnabled ||
              timeToTopUpEnabled ||
              invalidRecordEnabled) ||
          remindersEnabled;
    } catch (e) {
      Logger.error('Error checking monitoring status: $e');
      return false;
    }
  }

  /// Update monitoring based on current settings
  Future<void> updateMonitoring() async {
    try {
      final shouldMonitor = await shouldMonitoringBeActive();

      if (shouldMonitor) {
        await startMonitoring();
      } else {
        await stopMonitoring();
      }
    } catch (e) {
      Logger.error('Error updating monitoring: $e');
    }
  }

  /// Check if background monitoring is functioning properly
  Future<Map<String, dynamic>> getMonitoringStatus() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final lastCheckTime = prefs.getString('background_alert_check_time');
      final checkRequested =
          prefs.getBool('background_alert_check_requested') ?? false;
      final checkFailed =
          prefs.getBool('background_alert_check_failed') ?? false;
      final failureReason = prefs.getString('background_alert_failure_reason');

      final shouldBeActive = await shouldMonitoringBeActive();

      DateTime? lastCheck;
      if (lastCheckTime != null) {
        try {
          lastCheck = DateTime.parse(lastCheckTime);
        } catch (e) {
          Logger.warning('Failed to parse last check time: $e');
        }
      }

      final timeSinceLastCheck =
          lastCheck != null ? DateTime.now().difference(lastCheck) : null;

      final isHealthy = shouldBeActive
          ? (lastCheck != null &&
              timeSinceLastCheck != null &&
              timeSinceLastCheck.inHours < 12 &&
              !checkFailed)
          : true;

      return {
        'isActive': shouldBeActive,
        'isHealthy': isHealthy,
        'lastCheckTime': lastCheckTime,
        'timeSinceLastCheck': timeSinceLastCheck?.inMinutes,
        'checkRequested': checkRequested,
        'checkFailed': checkFailed,
        'failureReason': failureReason,
      };
    } catch (e) {
      Logger.error('Error getting monitoring status: $e');
      return {
        'isActive': false,
        'isHealthy': false,
        'error': e.toString(),
      };
    }
  }
}

/// Background task callback dispatcher
@pragma('vm:entry-point')
void _callbackDispatcher() {
  Workmanager().executeTask((task, inputData) async {
    try {
      Logger.info('Background notification check started - Task: $task');

      // Initialize timezone data for background context
      tz.initializeTimeZones();

      // Log system information for debugging
      Logger.info('Background task running in isolate at ${DateTime.now()}');

      // Load notification settings
      final prefs = await SharedPreferences.getInstance();
      final lowBalanceEnabled =
          prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false;
      final timeToTopUpEnabled =
          prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false;
      final invalidRecordEnabled =
          prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false;
      final remindersEnabled =
          prefs.getBool(PreferenceKeys.remindersEnabled) ?? false;

      final anyNotificationEnabled =
          lowBalanceEnabled || timeToTopUpEnabled || invalidRecordEnabled;

      if (!anyNotificationEnabled && !remindersEnabled) {
        Logger.info('Notifications and reminders disabled, skipping check');
        return Future.value(true);
      }

      // Background task handles both reminders and critical alerts
      Logger.info(
          'Background task running - checking reminders and critical alerts');

      // Initialize notification service for background context
      try {
        await _initializeBackgroundNotificationService();
      } catch (e) {
        Logger.error(
            'Failed to initialize background notification service: $e');
        return Future.value(false);
      }

      // Check and update reminders if enabled
      if (remindersEnabled) {
        try {
          final reminderService = ReminderSchedulingService();
          await reminderService.updateReminders();
          Logger.info('Reminder maintenance completed');
        } catch (e) {
          Logger.error('Reminder maintenance failed: $e');
        }
      }

      // Check critical alerts if any notification type enabled
      if (anyNotificationEnabled) {
        try {
          await _checkCriticalAlertsInBackground();
          Logger.info('Background alert check completed successfully');
        } catch (e) {
          Logger.warning('Background alert check failed: $e');
          // Store failure information for foreground recovery
          final prefs = await SharedPreferences.getInstance();
          await prefs.setBool('background_alert_check_failed', true);
          await prefs.setString(
              'background_alert_failure_reason', e.toString());
        }
      }

      // Process notification queue for failed notifications
      try {
        final queueManager = NotificationQueueManager();
        await queueManager.processQueue();
        Logger.info('Notification queue processing completed');
      } catch (e) {
        Logger.error('Notification queue processing failed: $e');
      }

      // Process pending Firebase messages
      try {
        final firebaseService = FirebaseMessagingService();
        if (firebaseService.isAvailable) {
          await firebaseService.processPendingMessages();
          Logger.info('Firebase pending messages processed');
        }
      } catch (e) {
        Logger.error('Firebase message processing failed: $e');
      }

      // Ensure foreground service is running if notifications enabled
      try {
        if (anyNotificationEnabled) {
          final foregroundService = ForegroundServiceManager();
          final isRunning = await foregroundService.isServiceRunning();
          if (!isRunning) {
            await foregroundService.startService();
            Logger.info(
                'Foreground service started for notification reliability');
          }
        }
      } catch (e) {
        Logger.error('Foreground service management failed: $e');
      }

      return Future.value(true);
    } catch (e) {
      Logger.error('Background task failed: $e');
      return Future.value(false);
    }
  });
}

/// Check critical alerts in background context
Future<void> _checkCriticalAlertsInBackground() async {
  try {
    // Import required for background context
    // Note: This runs in isolate, so we need to recreate services
    final prefs = await SharedPreferences.getInstance();

    // Load alert settings
    final lowBalanceEnabled =
        prefs.getBool(PreferenceKeys.lowBalanceAlertsEnabled) ?? false;
    final timeToTopUpEnabled =
        prefs.getBool(PreferenceKeys.timeToTopUpAlertsEnabled) ?? false;
    final invalidRecordEnabled =
        prefs.getBool(PreferenceKeys.invalidRecordAlertsEnabled) ?? false;

    // Check if any notification type is enabled (individual types only)
    final anyNotificationEnabled =
        lowBalanceEnabled || timeToTopUpEnabled || invalidRecordEnabled;

    if (!anyNotificationEnabled) {
      Logger.info('Background alerts: No alerts enabled, skipping');
      return;
    }

    // For background context, we'll use a simplified approach
    // Store a flag that foreground should check alerts immediately
    await prefs.setBool('background_alert_check_requested', true);
    await prefs.setString(
        'background_alert_check_time', DateTime.now().toIso8601String());

    // Clear any previous failure flags
    await prefs.remove('background_alert_check_failed');
    await prefs.remove('background_alert_failure_reason');

    // Store which alert types are enabled for foreground processing
    await prefs.setBool('background_low_balance_enabled', lowBalanceEnabled);
    await prefs.setBool('background_time_to_topup_enabled', timeToTopUpEnabled);
    await prefs.setBool(
        'background_invalid_record_enabled', invalidRecordEnabled);

    Logger.info(
        'Background alerts: Requested foreground alert check with settings stored');
  } catch (e) {
    Logger.error('Background alerts: Error checking critical alerts: $e');
    rethrow; // Re-throw to trigger error handling in caller
  }
}

/// Initialize notification service for background context
Future<void> _initializeBackgroundNotificationService() async {
  try {
    // Initialize basic notification plugin for background use
    final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings();

    const InitializationSettings initializationSettings =
        InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );

    await flutterLocalNotificationsPlugin.initialize(initializationSettings);
    Logger.info('Background notification service initialized');
  } catch (e) {
    Logger.error('Failed to initialize background notification service: $e');
    rethrow;
  }
}
